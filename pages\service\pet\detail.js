import user from '../../../api/modules/user';
import orderApi from '../../../api/modules/order';
const utils = require('../../utils/util');
import siteinfo from "../../../siteinfo";

Page({
  data: {
    userInfo: null,
    siteinfo,
    petInfo: null,
    lastServiceData: null,
    recentOrders: [],
    loading: true,
    petId: null,
  },

  onLoad(options) {
    console.log('宠物详情页面加载，参数:', options);

    // 使用页面扩展的用户信息获取方式
    const userInfo = this.data.userInfo || this.$session?.getUser();
    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }

    const { petId } = options;
    if (!petId) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }

    console.log('用户信息:', userInfo);
    console.log('宠物ID:', petId);

    this.setData({
      userInfo,
      petId: parseInt(petId)
    });

    this.loadPetDetail();
  },

  /**
   * 加载宠物详情
   */
  async loadPetDetail() {
    try {
      wx.showLoading({ title: '加载中...' });

      const { userInfo, petId } = this.data;
      console.log('开始加载宠物详情，用户ID:', userInfo.id, '宠物ID:', petId);

      // 先只加载宠物信息，简化调试
      const pets = await user.getPets(userInfo.id);
      console.log('获取到的宠物列表:', pets);

      // 找到当前宠物信息
      const petInfo = pets.find(pet => pet.id === petId);
      if (!petInfo) {
        console.error('未找到宠物信息，petId:', petId, '宠物列表:', pets);
        wx.showToast({
          title: '宠物信息不存在',
          icon: 'none'
        });
        wx.navigateBack();
        return;
      }

      console.log('找到宠物信息:', petInfo);

      // 格式化宠物年龄
      if (typeof petInfo.bri === 'number' && !isNaN(petInfo.bri)) {
        petInfo.formattedBri = utils.formatAge(petInfo.bri);
      } else {
        petInfo.formattedBri = '无效年龄';
      }

      // 格式化生日
      if (petInfo.birthday) {
        petInfo.formattedBirthday = utils.formatDateTime(petInfo.birthday, 'YYYY-MM-DD');
      }

      this.setData({
        petInfo,
        lastServiceData: { formattedLastServiceTime: '暂无洗护记录' },
        loading: false
      });

      console.log('宠物详情页面数据设置完成');

      // 暂时注释掉订单记录加载
      // this.loadRecentOrders();

    } catch (error) {
      console.error('加载宠物详情失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 获取最后洗护时间
   */
  async getLastServiceTime(userId, petId) {
    try {
      const data = await user.getLastServiceTime(userId, petId);
      if (data.lastServiceTime) {
        data.formattedLastServiceTime = utils.formatLastServiceTime(data.lastServiceTime);
        data.formattedServiceTime = data.serviceTime ? utils.formatNormalDate(data.serviceTime) : '';
      }
      return data;
    } catch (error) {
      console.log('获取最后洗护时间失败:', error);
      return {
        lastServiceTime: null,
        formattedLastServiceTime: '暂无洗护记录'
      };
    }
  },

  /**
   * 加载最近的订单记录
   */
  async loadRecentOrders() {
    try {
      const { userInfo, petId } = this.data;
      
      // 获取用户所有订单
      const orders = await orderApi.getlists(userInfo.id, 'all');
      
      // 筛选出该宠物的订单，并按时间排序
      const petOrders = orders
        .filter(order => order.petId === petId)
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 5) // 只显示最近5条
        .map(order => ({
          ...order,
          formattedCreatedAt: utils.formatNormalDate(order.createdAt),
          formattedServiceTime: order.serviceTime ? utils.formatNormalDate(order.serviceTime) : '',
          formattedTotalFee: (order.totalFee * 1).toFixed(2)
        }));

      this.setData({
        recentOrders: petOrders
      });

    } catch (error) {
      console.error('加载订单记录失败:', error);
      // 不显示错误提示，因为这不是关键功能
    }
  },

  /**
   * 编辑宠物信息
   */
  editPet() {
    const { petInfo } = this.data;
    wx.setStorageSync('editPetItem', petInfo);
    wx.navigateTo({
      url: '/pages/service/pet/addPet'
    });
  },

  /**
   * 查看订单详情
   */
  viewOrderDetail(e) {
    const { orderId } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/serviceOrder/orderDetail/index?orderId=${orderId}`
    });
  },

  /**
   * 预览头像
   */
  previewAvatar() {
    const { petInfo } = this.data;
    if (petInfo.avatar) {
      wx.previewImage({
        current: petInfo.avatar,
        urls: [petInfo.avatar]
      });
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadPetDetail().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    const { petInfo } = this.data;
    return {
      title: `我的爱宠 ${petInfo?.name || ''}`,
      path: `/pages/service/pet/detail?petId=${this.data.petId}`,
      imageUrl: petInfo?.avatar || ''
    };
  }
});
