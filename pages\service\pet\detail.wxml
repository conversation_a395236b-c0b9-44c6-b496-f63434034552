<view class="container pet-detail-container">
  <view wx:if="{{loading}}" class="loading-container">
    <text class="loading-text">加载中...</text>
  </view>

  <view wx:elif="{{!petInfo}}" class="error-container">
    <text class="error-text">宠物信息加载失败</text>
  </view>

  <view wx:else class="pet-detail-content">
    <!-- 简化版宠物基本信息卡片 -->
    <view class="pet-info-card">
      <view class="pet-avatar-section" bind:tap="previewAvatar">
        <image
          src="{{petInfo.avatar || siteinfo.constant.pet[petInfo.type]}}"
          class="pet-avatar"
          mode="aspectFill"
        ></image>
      </view>

      <view class="pet-basic-info">
        <view class="pet-name-section">
          <text class="pet-name">{{petInfo.name}}</text>
          <view class="pet-gender" style="background-color: {{petInfo.gender === 1 ? 'rgba(122, 221, 252, 0.8)' : petInfo.gender === 2 ? 'rgba(255, 192, 218, 0.8)' : 'rgba(255, 235, 59, 0.8)'}};">
            {{petInfo.gender === 1 ? '弟弟' : petInfo.gender === 2 ? '妹妹' : '未知'}}
          </view>
        </view>

        <view class="pet-details">
          <view class="detail-item">
            <text class="detail-label">年龄：</text>
            <text class="detail-value">{{petInfo.formattedBri}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">品种：</text>
            <text class="detail-value">{{petInfo.breed || '未知'}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">类型：</text>
            <text class="detail-value">{{petInfo.type === 'cat' ? '猫咪' : petInfo.type === 'dog' ? '狗狗' : '其他'}}</text>
          </view>
        </view>
      </view>

      <view class="edit-btn" bind:tap="editPet">
        <text class="diy-icon-writefill edit-icon"></text>
        <text class="edit-text">编辑</text>
      </view>
    </view>

    <!-- 测试信息 -->
    <view class="test-info">
      <text>宠物ID: {{petId}}</text>
      <text>用户ID: {{userInfo.id}}</text>
      <text>页面跳转成功！</text>
    </view>
  </view>
</view>
