.pet-detail-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

.pet-detail-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 宠物基本信息卡片 */
.pet-info-card {
  background: white;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.pet-avatar-section {
  display: flex;
  justify-content: center;
  margin-bottom: 20rpx;
}

.pet-avatar {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  border: 4rpx solid #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.pet-basic-info {
  text-align: center;
}

.pet-name-section {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.pet-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.pet-gender {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #333;
}

.pet-details {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.edit-btn {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx;
  background: rgba(47, 131, 255, 0.1);
  border-radius: 16rpx;
}

.edit-icon {
  font-size: 32rpx;
  color: rgba(47, 131, 255, 1);
}

.edit-text {
  font-size: 20rpx;
  color: rgba(47, 131, 255, 1);
}

/* 健康状态卡片 */
.health-status-card {
  background: white;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-title {
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.health-items {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.health-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
}

.health-label {
  font-size: 28rpx;
  color: #666;
}

.health-value {
  font-size: 28rpx;
  font-weight: 500;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

.status-yes {
  color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
}

.status-no {
  color: #ff4d4f;
  background: rgba(255, 77, 79, 0.1);
}

/* 最后洗护记录卡片 */
.last-service-card {
  background: white;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.service-info {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.service-item:last-child {
  border-bottom: none;
}

.service-label {
  font-size: 28rpx;
  color: #666;
}

.service-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.no-service {
  text-align: center;
  padding: 40rpx 0;
}

.no-service-text {
  font-size: 28rpx;
  color: #999;
}

/* 最近订单记录 */
.recent-orders-card {
  background: white;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 40rpx;
}

.orders-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.order-item {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 1rpx solid #e9ecef;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.order-sn {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.order-status {
  font-size: 24rpx;
  color: #666;
  padding: 4rpx 12rpx;
  background: rgba(47, 131, 255, 0.1);
  border-radius: 12rpx;
}

.order-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.order-time,
.order-amount {
  font-size: 24rpx;
  color: #666;
}

.order-amount {
  color: #ff4d4f;
  font-weight: 500;
}

.order-service-time {
  font-size: 24rpx;
  color: #666;
}
