import user from "../../../api/modules/user";
const utils = require('../../utils/util');
import siteinfo from "../../../siteinfo";

// pages/service/pet/index.js
Page({
  data: {
    //用户全局信息
    userInfo: null,
    siteinfo,
    checkedId: null,
    listNum: 2,
    bri: "计算中",
    isService: false,
    list: {
      rows: [],
      total: 0
    },
    title: ''
  },
  onLoad(options) {
    const userInfo = this.data.userInfo;
    if (!userInfo) {
      wx.navigateTo({
        url: "/pages/login/index",
      });
      return;
    }
    if (options && options.isService) {
      this.setData({
        isService: options.isService
      })
    }
    this.init();
  },

  onShow() {
    const item = wx.getStorageSync('selectPetInfo');
    if (item && this.data.isService) {
      this.setData({
        checkedId: item.id
      })
    }
    this.getPetList()
  },

  async getPetList() {
    try {
      const data = await user.getPets(this.data.userInfo.id);

      // 并行获取每个宠物的最后洗护时间
      const formattedData = await Promise.all(data.map(async item => {
        // 格式化年龄
        if (typeof item.bri === 'number' && !isNaN(item.bri)) {
          item.formattedBri = utils.formatAge(item.bri);
        } else {
          item.formattedBri = '无效年龄';
        }

        // 获取最后洗护时间
        try {
          const lastServiceData = await user.getLastServiceTime(this.data.userInfo.id, item.id);
          item.lastServiceTime = lastServiceData.lastServiceTime;
          item.formattedLastServiceTime = utils.formatLastServiceTime(lastServiceData.lastServiceTime);
        } catch (error) {
          console.log(`获取宠物${item.name}最后洗护时间失败:`, error);
          item.lastServiceTime = null;
          item.formattedLastServiceTime = '暂无洗护记录';
        }

        return item;
      }));

      this.setData({
        list: {
          rows: formattedData,
          total: formattedData.length
        }
      });
    } catch (err) {
      console.log(err);
    }
  },

  async deletePet(id) {
    wx.showLoading({
      title: "删除中..."
    });
    await user.delPet(this.data.userInfo.id, id).then(() => {
      wx.showToast({
        title: '删除成功',
      });
      this.getPetList();
    }).catch((error) => {
      console.error('删除失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: "error"
      });
    }).finally(() => {
      wx.hideLoading();
    })
  },

  async editRedirect(event) {
    const item = event.currentTarget.dataset.item;
    wx.setStorageSync('editPetItem', item);
    wx.navigateTo({
      url: '/pages/service/pet/addPet',
    });
  },
  async init() {},
  redirect() {
    wx.navigateTo({
      url: '/pages/service/pet/addPet',
    })
  },
  deletePetModal(event) {
    const item = event.currentTarget.dataset.item;
    const that = this;
    wx.showModal({
      title: '删除提示',
      content: `确定要删除 ${item.name} 吗？`,
      success(res) {
        if (res.confirm) {
          that.deletePet(item.id)
        }
      }
    });
  },

  selectPet(event) {
    if(this.data.isService){
      const item = event.currentTarget.dataset.item;
      wx.setStorageSync('selectPetInfo', item);
      wx.redirectTo({
        url: `/pages/service/index`,
      })
    }
  }
})