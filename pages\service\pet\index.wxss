/* pages/service/pet/index.wxss */
.containerpet{
  padding-bottom: 120rpx;
}
.flex-pet {
  padding: 30rpx;
  border-radius: 24rpx;
  margin: 15rpx 20rpx 15rpx 20rpx;
  background-color: rgba(255, 192, 218, 0.3);
  overflow: hidden;
  width: calc(100% - 40rpx) !important;
}

.choosen {
  border: 2rpx solid rgba(47, 131, 255, 1);
  position: relative;
}

.choose-icon {
  position: absolute;
  top: -2rpx;
  right: -4rpx;
}

.choose-icon image {
  width: 50rpx;
}

.image7-clz {
  border-radius: 24rpx;
  overflow: hidden;
}

.image7-size {
  border-radius: 30rpx;
  height: 160rpx !important;
  width: 160rpx !important;
}

.flex48-clz {
  padding-top: 0rpx;
  flex: 1;
  padding-left: 20rpx;
  padding-bottom: 0rpx;
  padding-right: 0rpx;
}

.text23-clz {
  font-size: 30rpx !important;
  font-weight: bold;
  color: #333;
}

.flex49-clz {
  margin: 6rpx 0;
  width: calc(100% - 0rpx - 0rpx) !important;
  flex-direction: column;
}

.text24-pet-ly {
  line-height: 50rpx;
  font-size: 24rpx !important;
  color: #666;
}

.lastServiceInfo {
  margin-top: 8rpx;
}

.lastServiceTime {
  font-size: 22rpx;
  color: #999;
  line-height: 40rpx;
}

.flex1-clz {
  border-top: 2rpx solid #e4e4e4;
  padding: 16rpx;
  border-radius: 24rpx 24rpx 0rpx 0rpx;
  bottom: 0rpx;
  background-color: #ffffff;
  box-shadow: 0rpx 4rpx 12rpx rgba(31, 31, 31, 0.16);
  overflow: visible;
  left: 0rpx;
}

.flex12-clz {
  flex: 1;
}

.btn-clz {
  padding: 20rpx;
  border-radius: 40rpx;
  color: #fff;
  font-size: 28rpx !important;
  background-color: rgba(255, 67, 145, 1);
  overflow: hidden;
  text-align: center;
}

.deleteIcon {
  display: flex;
  position: absolute;
  right: 5%;
  bottom: 12%;
  color: #ff4d4d;
}

.deleteIcon .delete-text {
  margin-left: 8rpx;
}

.editIcon {
  display: flex;
  position: absolute;
  right: 20%;
  bottom: 12%;
  font-size: 26rpx;
  color: rgba(47, 131, 255, 1);
}

.editIcon .edit-text {
  margin-left: 8rpx;
}

.petInfo {
  position: absolute;
  top: 25%;
}

.petInfoAge {
  margin: 10rpx 0;
}
.selectPet{
  position: absolute;
  right: 5%;
  bottom: 15%;
  color: rgba(47, 131, 255, 1);
}